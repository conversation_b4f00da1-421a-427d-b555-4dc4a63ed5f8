from django.db import models

from .user import User


class Story(models.Model):
  id = models.IntegerField(primary_key=True)
  title = models.CharField(max_length=255, null=False, blank=False)
  text = models.TextField(null=True, blank=True)
  user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
  score = models.IntegerField(null=True, blank=True)
  url = models.URLField(null=True, blank=True)
  is_deleted = models.BooleanField(default=False)
  created_on = models.DateTimeField()
  updated_on = models.DateTimeField(auto_now=True)
  story_score = models.FloatField(default=0.0)

  def save(self, *args, **kwargs):
    super().save(*args, **kwargs)
    
    # Calculate story_score for both new and existing stories
    try:
      comment_count = self.comments.count()
      self.generate_story_score(comment_count)
    except AttributeError:
      self.generate_story_score(0)

  class Meta:
    db_table = "story"
    verbose_name_plural = "Stories"

  def generate_story_score(self, comment_count: int) -> None:
    self.story_score = (self.score if self.score else 0) + (comment_count * 0.5)
    # Use update to avoid recursive save
    Story.objects.filter(pk=self.pk).update(story_score=self.story_score)
  
  def __str__(self):
    return f"{self.id} - {self.title[:20]}..." if len(self.title) > 20 else f"{self.id} - {self.title}"


