from django.core.management.base import BaseCommand
from django.db.models import Count

from hacker_news.models import Story

class Command(BaseCommand):
    help = 'Update story_score for all stories'

    def handle(self, *args, **options):
        stories = Story.objects.annotate(comment_count=Count('comments'))
        
        count = 0
        for story in stories:
            old_score = story.story_score
            story.generate_story_score(story.comment_count)
            if old_score != story.story_score:
                count += 1
        
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {count} story scores'))