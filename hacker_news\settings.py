"""
Django settings for hacker_news project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
from pathlib import Path

from configurations import Configuration


BASE_DIR = Path(__file__).resolve().parent.parent


class Base(Configuration):
  SECRET_KEY = os.getenv("SECRET_KEY")
  DEBUG = True
  ALLOWED_HOSTS = ["*"]

  CSRF_COOKIE_SECURE = False
  CSRF_COOKIE_HTTPONLY = False
  SESSION_COOKIE_SECURE = False

  INSTALLED_APPS = [
    "whitenoise.runserver_nostatic",
    "django.contrib.admin.apps.SimpleAdminConfig",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "corsheaders",
    "djangorestframework_camel_case",
    "hacker_news.apps.HackerNewsConfig"
  ]

  MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "hacker_news.middleware.slash_middleware.AppendOrRemoveSlashMiddleware",
    "hacker_news.middleware.model_admin_reorder.ModelAdminReorder",
  ]

  AUTHENTICATION_BACKENDS = (
    "django.contrib.auth.backends.ModelBackend",
  )

  STORAGES = {
    "staticfiles": {
      "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
  }

  REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
      "rest_framework.authentication.SessionAuthentication",
    ),
    "DEFAULT_RENDERER_CLASSES": (
      "djangorestframework_camel_case.render.CamelCaseJSONRenderer",
      "djangorestframework_camel_case.render.CamelCaseBrowsableAPIRenderer",
    ),
    "DEFAULT_PARSER_CLASSES": ("djangorestframework_camel_case.parser.CamelCaseJSONParser",),
  }
  APPEND_SLASH = False

  ROOT_URLCONF = "hacker_news.urls"

  TEMPLATES = [
    {
      "BACKEND": "django.template.backends.django.DjangoTemplates",
      "DIRS": [],
      "APP_DIRS": True,
      "OPTIONS": {
        "context_processors": [
          "django.template.context_processors.request",
          "django.contrib.auth.context_processors.auth",
          "django.contrib.messages.context_processors.messages",
        ],
      },
    },
  ]

  WSGI_APPLICATION = "hacker_news.wsgi.application"

  AUTH_PASSWORD_VALIDATORS = [
    {
      "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
      "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
      "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
      "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
  ]

  ADMIN_REORDER = (
    {
      "app": "hacker_news",
      "label": "Hacker News",
      "models": (
        "hacker_news.User",
        "hacker_news.Story",
        "hacker_news.Article",
        "hacker_news.Comment",
      ),
    },
    {
      "app": "auth",
      "label": "Django Auth",
      "models": (
        "auth.Group",
        "auth.User"
      ),
    },
  )

  STATIC_ROOT = BASE_DIR / "staticfiles"
  LANGUAGE_CODE = "en-us"
  TIME_ZONE = "UTC"
  USE_I18N = True
  USE_TZ = True
  STATIC_URL = "static/"
  DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
  VIEWS_DIR = os.path.join(BASE_DIR, "db_objects/views")
  FUNCTIONS_DIR = os.path.join(BASE_DIR, "db_objects/functions")

  DATABASES = {
    "default": {
      "ENGINE": "django.db.backends.postgresql_psycopg2",
      "NAME": os.getenv("DB_NAME"),
      "USER": os.getenv("DB_USER"),
      "PASSWORD": os.getenv("DB_PASSWORD"),
      "HOST": os.getenv("DB_HOST"),
      "PORT": os.getenv("DB_PORT"),
    }
  }

  OLLAMA_API_BASE = os.getenv("OLLAMA_API_BASE")

  REDIS_HOST = os.getenv("REDIS_HOST")
  REDIS_PORT = os.getenv("REDIS_PORT")
  REDIS_DB = os.getenv("REDIS_DB")

  CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL")
  CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND")


class Test(Base):
  DEBUG = True
  ALLOWED_HOSTS = ["*"]
  DATABASES = {
    "default": {
      "ENGINE": "django.db.backends.postgresql_psycopg2",
      "NAME": "hacker_news",
      "USER": "dg",
      "PASSWORD": "test1234",
      "HOST": "localhost",
      "PORT": "5432",
    }
  }


class Local(Base):
  DEBUG = True
  ALLOWED_HOSTS = ["*"]
